"""
Generic resource manager service implementation

Manages resources, connection pooling, and circuit breaker functionality
for service calls, implementing intelligent resource allocation and monitoring.
"""

import asyncio
from collections import deque
from datetime import datetime, timezone

from loguru import logger

from app.shared.interfaces.circuit_breaker_interface import CircuitBreakerInterface
from app.shared.interfaces.resource_manager_interface import ResourceManagerInterface
from app.shared.models.circuit_breaker_state import CircuitBreakerState
from app.shared.models.request_priority import RequestPriority
from app.shared.models.resource_status import ResourceStatus
from app.shared.models.service_config import ServiceConfig


class GenericResourceManager(ResourceManagerInterface):
    """
    Generic implementation of resource management and monitoring

    Provides intelligent resource allocation, circuit breaker functionality,
    and comprehensive monitoring of system resources and performance.
    """

    def __init__(self, config: ServiceConfig, circuit_breaker: CircuitBreakerInterface):
        """
        Initialize resource manager with configuration

        Args:
            config: Service configuration containing resource settings
            circuit_breaker: Circuit breaker implementation for fault tolerance
        """
        self.config = config
        self.circuit_breaker = circuit_breaker

        # Resource allocation semaphores
        self._max_concurrent = config.max_concurrent_requests
        self._resource_semaphore = asyncio.Semaphore(self._max_concurrent)

        # Priority-based resource allocation
        self._priority_allocations = {
            RequestPriority.HIGH: int(
                self._max_concurrent * config.high_priority_allocation
            ),
            RequestPriority.MEDIUM: int(
                self._max_concurrent * config.medium_priority_allocation
            ),
            RequestPriority.LOW: int(
                self._max_concurrent * config.low_priority_allocation
            ),
        }

        # Ensure at least 1 resource per priority
        for priority in RequestPriority:
            self._priority_allocations[priority] = max(
                self._priority_allocations[priority], 1
            )

        # Resource usage tracking
        self._active_requests = 0
        self._resource_usage_history: deque[float] = deque(maxlen=100)
        self._last_health_check = datetime.now(timezone.utc)

        # Performance metrics
        self._response_times: deque[float] = deque(maxlen=100)
        self._success_count = 0
        self._failure_count = 0

        # Lock for thread-safe operations
        self._lock = asyncio.Lock()

        logger.debug(
            f"GenericResourceManager initialized for {config.name} with "
            f"{self._max_concurrent} max concurrent requests"
        )

    async def acquire_resource(self, priority: RequestPriority) -> bool:
        """
        Acquire resource for processing

        Args:
            priority: Request priority for resource allocation

        Returns:
            True if resource acquired, False if unavailable

        Raises:
            RuntimeError: If resource allocation fails
        """
        try:
            # Check circuit breaker first
            if not await self.check_circuit_breaker():
                logger.debug("Circuit breaker is open, rejecting resource request")
                return False

            # Try to acquire resource with timeout based on priority
            timeout_map = {
                RequestPriority.HIGH: 1.0,  # 1 second for high priority
                RequestPriority.MEDIUM: 5.0,  # 5 seconds for medium priority
                RequestPriority.LOW: 10.0,  # 10 seconds for low priority
            }

            timeout = timeout_map.get(priority, 5.0)

            try:
                await asyncio.wait_for(
                    self._resource_semaphore.acquire(), timeout=timeout
                )

                async with self._lock:
                    self._active_requests += 1

                logger.debug(f"Resource acquired for {priority.value} priority request")
                return True

            except asyncio.TimeoutError:
                logger.debug(
                    f"Resource acquisition timeout for {priority.value} "
                    f"priority request"
                )
                return False

        except Exception as e:
            logger.error(f"Error acquiring resource: {e}")
            return False

    async def release_resource(self) -> None:
        """
        Release resource after processing
        """
        try:
            async with self._lock:
                if self._active_requests > 0:
                    self._active_requests -= 1

            self._resource_semaphore.release()
            logger.debug("Resource released")

        except Exception as e:
            logger.error(f"Error releasing resource: {e}")

    async def get_resource_status(self) -> ResourceStatus:
        """
        Get current resource usage and status

        Returns:
            ResourceStatus containing utilization and health metrics
        """
        try:
            async with self._lock:
                # Calculate resource utilization
                utilization = (
                    (self._active_requests / self._max_concurrent) * 100.0
                    if self._max_concurrent > 0
                    else 0.0
                )

                # Get circuit breaker state
                cb_state = (
                    await self.circuit_breaker.get_state()
                    if hasattr(self.circuit_breaker, "get_state")
                    else CircuitBreakerState.CLOSED
                )

                return ResourceStatus(
                    gpu_utilization=utilization,  # Generic resource utilization
                    memory_usage=0.0,  # Would need system monitoring for actual memory
                    active_connections=self._active_requests,
                    max_connections=self._max_concurrent,
                    circuit_breaker_state=cb_state,
                    last_health_check=self._last_health_check,
                )

        except Exception as e:
            logger.error(f"Error getting resource status: {e}")
            return ResourceStatus(
                gpu_utilization=0.0,
                memory_usage=0.0,
                active_connections=0,
                max_connections=self._max_concurrent,
                circuit_breaker_state=CircuitBreakerState.OPEN,
                last_health_check=datetime.now(timezone.utc),
            )

    async def record_success(self, processing_time: float) -> None:
        """
        Record successful operation for metrics

        Args:
            processing_time: Time taken for the operation
        """
        try:
            async with self._lock:
                self._response_times.append(processing_time)
                self._success_count += 1
                self._last_health_check = datetime.now(timezone.utc)

            # Notify circuit breaker of success
            if hasattr(self.circuit_breaker, "record_success"):
                await self.circuit_breaker.record_success()

        except Exception as e:
            logger.error(f"Error recording success: {e}")

    async def record_failure(self, error: Exception) -> None:
        """
        Record failed operation for metrics and circuit breaker

        Args:
            error: Exception that caused the failure
        """
        try:
            async with self._lock:
                self._failure_count += 1
                self._last_health_check = datetime.now(timezone.utc)

            # Notify circuit breaker of failure
            if hasattr(self.circuit_breaker, "record_failure"):
                await self.circuit_breaker.record_failure(error)

        except Exception as e:
            logger.error(f"Error recording failure: {e}")

    async def check_circuit_breaker(self) -> bool:
        """
        Check if circuit breaker allows requests

        Returns:
            True if requests are allowed, False if circuit is open
        """
        try:
            if hasattr(self.circuit_breaker, "is_request_allowed"):
                result = await self.circuit_breaker.is_request_allowed()
                return bool(result)
            return True  # Default to allowing requests if no circuit breaker
        except Exception as e:
            logger.error(f"Error checking circuit breaker: {e}")
            return False

    async def get_performance_metrics(self) -> dict:
        """
        Get performance metrics for monitoring

        Returns:
            Dictionary containing performance metrics
        """
        try:
            async with self._lock:
                total_requests = self._success_count + self._failure_count
                success_rate = (
                    (self._success_count / total_requests * 100.0)
                    if total_requests > 0
                    else 0.0
                )

                avg_response_time = (
                    sum(self._response_times) / len(self._response_times)
                    if self._response_times
                    else 0.0
                )

                return {
                    "total_requests": total_requests,
                    "success_count": self._success_count,
                    "failure_count": self._failure_count,
                    "success_rate": success_rate,
                    "average_response_time": avg_response_time,
                    "active_requests": self._active_requests,
                    "max_concurrent": self._max_concurrent,
                    "utilization_percentage": (
                        self._active_requests / self._max_concurrent * 100.0
                    )
                    if self._max_concurrent > 0
                    else 0.0,
                }

        except Exception as e:
            logger.error(f"Error getting performance metrics: {e}")
            return {}

    async def initialize(self) -> None:
        """
        Initialize resource manager

        Raises:
            RuntimeError: If initialization fails
        """
        try:
            logger.info(
                f"Initializing GenericResourceManager for service: {self.config.name}"
            )

            # Initialize circuit breaker if it has initialization
            if hasattr(self.circuit_breaker, "initialize"):
                await self.circuit_breaker.initialize()

            logger.success(
                f"GenericResourceManager initialized successfully for service: "
                f"{self.config.name}"
            )
        except Exception as e:
            error_msg = f"Failed to initialize GenericResourceManager: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    async def cleanup(self) -> None:
        """
        Cleanup resource manager and release resources
        """
        try:
            logger.info(
                f"Cleaning up GenericResourceManager for service: {self.config.name}"
            )

            # Cleanup circuit breaker if it has cleanup
            if hasattr(self.circuit_breaker, "cleanup"):
                await self.circuit_breaker.cleanup()

            async with self._lock:
                # Clear metrics
                self._response_times.clear()
                self._resource_usage_history.clear()
                self._success_count = 0
                self._failure_count = 0
                self._active_requests = 0

            logger.success(
                f"GenericResourceManager cleaned up successfully for service: "
                f"{self.config.name}"
            )
        except Exception as e:
            logger.error(f"Error during GenericResourceManager cleanup: {e}")
            raise
