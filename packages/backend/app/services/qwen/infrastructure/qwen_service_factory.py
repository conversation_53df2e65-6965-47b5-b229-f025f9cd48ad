"""
Qwen service factory

Factory for creating Qwen-specific service instances using the generic
components while providing Qwen-specific configuration and customization.
"""

from typing import Any

from loguru import logger

from app.core.settings import Settings
from app.services.qwen.infrastructure.qwen_external_client import QwenExternalClient
from app.services.qwen.interfaces import (
    GenerationRequest,
    QwenServiceInterface,
)
from app.services.qwen.services.qwen_call_manager_impl import QwenCallManagerImpl
from app.services.qwen.services.qwen_service_impl import QwenServiceImpl
from app.shared.infrastructure import CircuitBreakerImpl
from app.shared.models.service_config import ServiceConfig
from app.shared.services.generic_queue_manager import GenericQueueManager
from app.shared.services.generic_resource_manager import GenericResourceManager


class QwenServiceFactory:
    """
    Factory for creating Qwen service instances

    Creates and configures all Qwen-specific components using the generic
    infrastructure while providing Qwen-specific optimizations and settings.
    """

    def __init__(self, settings: Settings):
        """
        Initialize Qwen service factory

        Args:
            settings: Application settings containing Qwen configuration
        """
        self.settings = settings
        self._initialized = False
        self._external_client: Any = None
        logger.debug("QwenServiceFactory initialized")

    def create_qwen_service(self) -> QwenServiceInterface:
        """
        Create complete Qwen service with all dependencies

        Returns:
            Configured QwenServiceInterface implementation
        """
        try:
            logger.info("Creating Qwen service with all dependencies")

            # Create Qwen-specific configuration
            config = ServiceConfig(
                name="qwen",
                max_concurrent_requests=self.settings.QWEN_MAX_CONCURRENT_REQUESTS,
                high_priority_allocation=0.6,
                medium_priority_allocation=0.3,
                low_priority_allocation=0.1,
                max_queue_size=self.settings.QWEN_MAX_QUEUE_SIZE,
                timeout=30.0,
                circuit_breaker_failure_threshold=5,
                circuit_breaker_recovery_timeout=60.0,
            )

            # Create generic components with Qwen-specific configuration
            queue_manager = GenericQueueManager[GenerationRequest](config)

            circuit_breaker = CircuitBreakerImpl(self.settings)

            resource_manager = GenericResourceManager(config, circuit_breaker)

            external_client = self.create_external_client()

            # Create Qwen call manager
            call_manager = QwenCallManagerImpl(
                settings=self.settings,
                queue_manager=queue_manager,
                resource_manager=resource_manager,
                external_client=external_client,
            )

            # Create Qwen service
            qwen_service = QwenServiceImpl(
                settings=self.settings,
                call_manager=call_manager,
            )

            logger.success("Qwen service created successfully")
            return qwen_service

        except Exception as e:
            error_msg = f"Failed to create Qwen service: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def create_qwen_call_manager(self) -> QwenCallManagerImpl:
        """
        Create Qwen call manager with dependencies

        Returns:
            Configured QwenCallManager instance
        """
        try:
            logger.info("Creating Qwen call manager")

            # Create configuration
            config = ServiceConfig(
                name="qwen",
                max_concurrent_requests=self.settings.QWEN_MAX_CONCURRENT_REQUESTS,
                max_queue_size=self.settings.QWEN_MAX_QUEUE_SIZE,
            )

            # Create dependencies
            queue_manager = GenericQueueManager[GenerationRequest](config)
            circuit_breaker = CircuitBreakerImpl(self.settings)
            resource_manager = GenericResourceManager(config, circuit_breaker)
            external_client = self.create_external_client()

            # Create call manager
            call_manager = QwenCallManagerImpl(
                settings=self.settings,
                queue_manager=queue_manager,
                resource_manager=resource_manager,
                external_client=external_client,
            )

            logger.success("Qwen call manager created successfully")
            return call_manager

        except Exception as e:
            error_msg = f"Failed to create Qwen call manager: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def create_queue_manager(self) -> Any:
        """Create queue manager"""
        from app.shared.models.service_config import ServiceConfig

        config = ServiceConfig(
            name="qwen",
            max_concurrent_requests=self.settings.QWEN_MAX_CONCURRENT_REQUESTS,
            max_queue_size=self.settings.QWEN_MAX_QUEUE_SIZE,
        )
        return GenericQueueManager[GenerationRequest](config)

    def create_resource_manager(self) -> Any:
        """Create resource manager"""
        from app.shared.models.service_config import ServiceConfig

        config = ServiceConfig(
            name="qwen",
            max_concurrent_requests=self.settings.QWEN_MAX_CONCURRENT_REQUESTS,
            max_queue_size=self.settings.QWEN_MAX_QUEUE_SIZE,
        )
        circuit_breaker = CircuitBreakerImpl(self.settings)
        return GenericResourceManager(config, circuit_breaker)

    def create_external_client(self) -> Any:
        """Create external client"""
        if self._external_client is None:
            self._external_client = QwenExternalClient(self.settings)
        return self._external_client

    def create_circuit_breaker(self) -> Any:
        """Create circuit breaker"""
        return CircuitBreakerImpl(self.settings)

    async def initialize_services(self) -> None:
        """
        Initialize all Qwen service components that require async initialization

        Raises:
            RuntimeError: If initialization fails
        """
        if self._initialized:
            return

        try:
            logger.info("Initializing Qwen service components")

            # Create and initialize external client
            external_client = self.create_external_client()
            await external_client.initialize()

            # Create and initialize call manager (this starts the background processor)
            call_manager = self.create_qwen_call_manager()
            await call_manager.initialize()

            self._initialized = True
            logger.success("Qwen service components initialized successfully")

        except Exception as e:
            error_msg = f"Failed to initialize Qwen service components: {e}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e
